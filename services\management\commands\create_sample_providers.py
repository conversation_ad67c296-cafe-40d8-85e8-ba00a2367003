from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from services.models import ServiceCategory, ServiceProvider, Service
from pets.models import PetCategory
from decimal import Decimal

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample service providers and services'

    def handle(self, *args, **options):
        # Get or create pet categories
        dog_category, _ = PetCategory.objects.get_or_create(
            name='Dog',
            defaults={'description': 'Dogs and puppies'}
        )
        cat_category, _ = PetCategory.objects.get_or_create(
            name='Cat',
            defaults={'description': 'Cats and kittens'}
        )

        # Get service categories
        grooming_category = ServiceCategory.objects.get(name='Pet Grooming')
        walking_category = ServiceCategory.objects.get(name='Pet Walking')
        training_category = ServiceCategory.objects.get(name='Pet Training')
        sitting_category = ServiceCategory.objects.get(name='Pet Sitting')

        # Sample providers data
        providers_data = [
            {
                'username': 'sarah_groomer',
                'first_name': '<PERSON>',
                'last_name': '<PERSON>',
                'email': '<EMAIL>',
                'bio': 'Professional pet groomer with 8 years of experience. Specializing in breed-specific cuts and gentle handling of anxious pets.',
                'experience_years': 8,
                'hourly_rate': Decimal('45.00'),
                'rating': Decimal('4.8'),
                'reviews_count': 127,
                'categories': [grooming_category],
                'pet_categories': [dog_category, cat_category],
                'services': [
                    {'name': 'Full Grooming Service', 'price': Decimal('65.00'), 'duration': 120},
                    {'name': 'Bath & Brush', 'price': Decimal('35.00'), 'duration': 60},
                    {'name': 'Nail Trimming', 'price': Decimal('15.00'), 'duration': 15},
                ]
            },
            {
                'username': 'mike_walker',
                'first_name': 'Mike',
                'last_name': 'Davis',
                'email': '<EMAIL>',
                'bio': 'Reliable dog walker and pet sitter. I love spending time with furry friends and ensuring they get the exercise they need.',
                'experience_years': 5,
                'hourly_rate': Decimal('25.00'),
                'rating': Decimal('4.6'),
                'reviews_count': 89,
                'categories': [walking_category, sitting_category],
                'pet_categories': [dog_category],
                'services': [
                    {'name': '30-minute Walk', 'price': Decimal('20.00'), 'duration': 30},
                    {'name': '60-minute Walk', 'price': Decimal('35.00'), 'duration': 60},
                    {'name': 'Pet Sitting (per day)', 'price': Decimal('80.00'), 'duration': 480},
                ]
            },
            {
                'username': 'emma_trainer',
                'first_name': 'Emma',
                'last_name': 'Wilson',
                'email': '<EMAIL>',
                'bio': 'Certified dog trainer specializing in obedience training and behavioral modification. Positive reinforcement methods only.',
                'experience_years': 12,
                'hourly_rate': Decimal('75.00'),
                'rating': Decimal('4.9'),
                'reviews_count': 156,
                'categories': [training_category],
                'pet_categories': [dog_category],
                'services': [
                    {'name': 'Basic Obedience Training', 'price': Decimal('100.00'), 'duration': 90},
                    {'name': 'Puppy Training', 'price': Decimal('80.00'), 'duration': 60},
                    {'name': 'Behavioral Consultation', 'price': Decimal('120.00'), 'duration': 60},
                ]
            },
        ]

        created_count = 0
        updated_count = 0

        for provider_data in providers_data:
            # Create or get user
            user, user_created = User.objects.get_or_create(
                username=provider_data['username'],
                defaults={
                    'first_name': provider_data['first_name'],
                    'last_name': provider_data['last_name'],
                    'email': provider_data['email'],
                    'is_service_provider': True,
                }
            )

            if user_created:
                user.set_password('testpass123')
                user.save()

            # Create or get service provider
            provider, provider_created = ServiceProvider.objects.get_or_create(
                user=user,
                defaults={
                    'bio': provider_data['bio'],
                    'experience_years': provider_data['experience_years'],
                    'hourly_rate': provider_data['hourly_rate'],
                    'rating': provider_data['rating'],
                    'reviews_count': provider_data['reviews_count'],
                }
            )

            if provider_created:
                # Add categories
                provider.categories.set(provider_data['categories'])
                provider.pet_categories.set(provider_data['pet_categories'])

                # Create services
                for service_data in provider_data['services']:
                    Service.objects.create(
                        provider=provider,
                        category=provider_data['categories'][0],  # Use first category
                        name=service_data['name'],
                        description=f"Professional {service_data['name'].lower()} service",
                        price=service_data['price'],
                        duration=service_data['duration'],
                    )

                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created provider: {user.get_full_name()}')
                )
            else:
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'Provider already exists: {user.get_full_name()}')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'\nSummary: {created_count} providers created, {updated_count} providers already existed'
            )
        )
