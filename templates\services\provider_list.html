{% extends 'base.html' %}

{% block title %}Pet Services | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .services-container {
        display: grid;
        grid-template-columns: 250px 1fr;
        gap: var(--gap-2xl);
    }

    @media (max-width: 992px) {
        .services-container {
            grid-template-columns: 1fr;
        }
    }

    .services-sidebar {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-xl);
        height: fit-content;
    }

    .sidebar-section {
        margin-bottom: var(--spacing-xl);
    }

    .sidebar-section:last-child {
        margin-bottom: 0;
    }

    .sidebar-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-base);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid var(--gray-200);
    }

    .category-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .category-item {
        margin-bottom: var(--spacing-xs);
    }

    .category-link {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: var(--spacing-sm) 0;
        color: var(--text);
        transition: var(--transition-base);
    }

    .category-link:hover {
        color: var(--primary);
    }

    .category-link.active {
        color: var(--primary);
        font-weight: var(--fw-medium);
    }

    .category-count {
        background-color: var(--gray-200);
        color: var(--text);
        font-size: var(--font-xs);
        padding: 2px 8px;
        border-radius: var(--radius-full);
    }

    .rating-filter {
        margin-top: var(--spacing-base);
    }

    .rating-options {
        display: flex;
        flex-direction: column;
        gap: var(--gap-xs);
    }

    .rating-option {
        display: flex;
        align-items: center;
        gap: var(--gap-xs);
    }

    .rating-stars {
        color: var(--warning);
        display: flex;
        gap: 2px;
    }

    .filter-button {
        width: 100%;
        margin-top: var(--spacing-base);
    }

    .services-content {
        display: flex;
        flex-direction: column;
    }

    .services-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-xl);
    }

    .services-title {
        margin: 0;
    }

    .services-actions {
        display: flex;
        align-items: center;
        gap: var(--gap-base);
    }

    .sort-select {
        padding: var(--spacing-xs) var(--spacing-base);
        border: 1px solid var(--gray-300);
        border-radius: var(--radius-md);
        background-color: var(--white);
    }

    .view-toggle {
        display: flex;
        gap: var(--gap-xs);
    }

    .view-toggle-button {
        background: none;
        border: 1px solid var(--gray-300);
        border-radius: var(--radius-md);
        padding: var(--spacing-xs) var(--spacing-sm);
        cursor: pointer;
        color: var(--text-light);
        transition: var(--transition-base);
    }

    .view-toggle-button:hover {
        color: var(--primary);
        border-color: var(--primary);
    }

    .view-toggle-button.active {
        background-color: var(--primary-light);
        color: var(--primary);
        border-color: var(--primary);
    }

    .provider-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: var(--gap-xl);
    }

    .provider-list {
        display: flex;
        flex-direction: column;
        gap: var(--gap-base);
    }

    .provider-card {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        overflow: hidden;
        transition: var(--transition-base);
    }

    .provider-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .provider-card-image {
        height: 200px;
        overflow: hidden;
        position: relative;
    }

    .provider-card-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: var(--transition-base);
    }

    .provider-card:hover .provider-card-image img {
        transform: scale(1.05);
    }

    .provider-placeholder {
        width: 100%;
        height: 100%;
        background: var(--gray-100);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .provider-placeholder i {
        font-size: 3rem;
        color: var(--gray-400);
    }

    .provider-card-content {
        padding: var(--spacing-base) var(--spacing-xl);
    }

    .provider-card-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-xs);
    }

    .provider-card-category {
        color: var(--text-light);
        margin-bottom: var(--spacing-sm);
        font-size: var(--font-sm);
    }

    .provider-card-rating {
        display: flex;
        align-items: center;
        gap: var(--gap-xs);
        margin-bottom: var(--spacing-base);
    }

    .provider-card-rating-stars {
        color: var(--warning);
    }

    .provider-card-rating-count {
        color: var(--text-light);
        font-size: var(--font-sm);
    }

    .provider-card-description {
        margin-bottom: var(--spacing-base);
        color: var(--text-light);
        font-size: var(--font-sm);
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .provider-card-services {
        display: flex;
        flex-wrap: wrap;
        gap: var(--gap-xs);
        margin-bottom: var(--spacing-base);
    }

    .provider-card-service {
        background-color: var(--primary-light);
        color: var(--primary);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-full);
        font-size: var(--font-xs);
    }

    .provider-list-item {
        display: grid;
        grid-template-columns: 200px 1fr auto;
        gap: var(--gap-xl);
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        overflow: hidden;
        transition: var(--transition-base);
    }

    .provider-list-item:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .provider-list-image {
        height: 200px;
        overflow: hidden;
        position: relative;
    }

    .provider-list-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: var(--transition-base);
    }

    .provider-list-item:hover .provider-list-image img {
        transform: scale(1.05);
    }

    .provider-list-content {
        padding: var(--spacing-xl);
        display: flex;
        flex-direction: column;
    }

    .provider-list-title {
        font-size: var(--font-xl);
        margin-bottom: var(--spacing-xs);
    }

    .provider-list-category {
        color: var(--text-light);
        font-size: var(--font-sm);
        margin-bottom: var(--spacing-base);
    }

    .provider-list-rating {
        display: flex;
        align-items: center;
        gap: var(--gap-xs);
        margin-bottom: var(--spacing-base);
    }

    .provider-list-description {
        margin-bottom: var(--spacing-base);
        color: var(--text-light);
    }

    .provider-list-services {
        display: flex;
        flex-wrap: wrap;
        gap: var(--gap-xs);
        margin-top: auto;
    }

    .provider-list-actions {
        padding: var(--spacing-xl);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: var(--gap-base);
    }

    .pagination-container {
        margin-top: var(--spacing-3xl);
        display: flex;
        justify-content: center;
    }

    .become-provider-banner {
        background-color: var(--primary-light);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-2xl);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .banner-content {
        flex: 1;
    }

    .banner-title {
        font-size: var(--font-xl);
        margin-bottom: var(--spacing-xs);
        color: var(--primary-dark);
    }

    .banner-text {
        color: var(--text);
        margin-bottom: 0;
    }

    .banner-button {
        margin-left: var(--spacing-xl);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="become-provider-banner">
        <div class="banner-content">
            <h2 class="banner-title">Become a Service Provider</h2>
            <p class="banner-text">Share your pet care skills and earn money by offering services to pet owners.</p>
        </div>
        <a href="{% url 'become-provider' %}" class="btn btn-primary banner-button">Get Started</a>
    </div>

    <div class="services-container">
        <div class="services-sidebar">
            <div class="sidebar-section">
                <h3 class="sidebar-title">Categories</h3>
                <ul class="category-list">
                    <li class="category-item">
                        <a href="{% url 'provider-list' %}" class="category-link {% if not current_category %}active{% endif %}">
                            All Services
                            <span class="category-count">{{ providers.count }}</span>
                        </a>
                    </li>
                    {% for category in categories %}
                        <li class="category-item">
                            <a href="{% url 'provider-list' %}?category={{ category.slug }}" class="category-link {% if current_category == category.slug %}active{% endif %}">
                                {{ category.name }}
                                <span class="category-count">{{ category.providers.count }}</span>
                            </a>
                        </li>
                    {% endfor %}
                </ul>
            </div>

            <div class="sidebar-section">
                <h3 class="sidebar-title">Pet Type</h3>
                <ul class="category-list">
                    <li class="category-item">
                        <a href="{% url 'provider-list' %}{% if current_category %}?category={{ current_category }}{% endif %}" class="category-link {% if not current_pet_category %}active{% endif %}">
                            All Pets
                        </a>
                    </li>
                    {% for pet_category in pet_categories %}
                        <li class="category-item">
                            <a href="{% url 'provider-list' %}?{% if current_category %}category={{ current_category }}&{% endif %}pet_category={{ pet_category.slug }}" class="category-link {% if current_pet_category == pet_category.slug %}active{% endif %}">
                                {{ pet_category.name }}
                            </a>
                        </li>
                    {% endfor %}
                </ul>
            </div>

            <div class="sidebar-section">
                <h3 class="sidebar-title">Rating</h3>
                <form method="get" class="rating-filter">
                    <div class="rating-options">
                        <label class="rating-option">
                            <input type="radio" name="rating" value="4" {% if request.GET.rating == '4' %}checked{% endif %}>
                            <div class="rating-stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                            </div>
                            <span>& Up</span>
                        </label>

                        <label class="rating-option">
                            <input type="radio" name="rating" value="3" {% if request.GET.rating == '3' %}checked{% endif %}>
                            <div class="rating-stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                                <i class="far fa-star"></i>
                            </div>
                            <span>& Up</span>
                        </label>

                        <label class="rating-option">
                            <input type="radio" name="rating" value="2" {% if request.GET.rating == '2' %}checked{% endif %}>
                            <div class="rating-stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                                <i class="far fa-star"></i>
                                <i class="far fa-star"></i>
                            </div>
                            <span>& Up</span>
                        </label>

                        <label class="rating-option">
                            <input type="radio" name="rating" value="1" {% if request.GET.rating == '1' %}checked{% endif %}>
                            <div class="rating-stars">
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                                <i class="far fa-star"></i>
                                <i class="far fa-star"></i>
                                <i class="far fa-star"></i>
                            </div>
                            <span>& Up</span>
                        </label>
                    </div>

                    {% if current_category %}
                        <input type="hidden" name="category" value="{{ current_category }}">
                    {% endif %}

                    {% if current_pet_category %}
                        <input type="hidden" name="pet_category" value="{{ current_pet_category }}">
                    {% endif %}

                    {% if search_query %}
                        <input type="hidden" name="search" value="{{ search_query }}">
                    {% endif %}

                    <button type="submit" class="btn btn-primary filter-button">Apply Filter</button>
                </form>
            </div>
        </div>

        <div class="services-content">
            <div class="services-header">
                <h1 class="services-title">Pet Services</h1>

                <div class="services-actions">
                    <select name="sort_by" id="sort-select" class="sort-select">
                        <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Name (A-Z)</option>
                        <option value="-name" {% if sort_by == '-name' %}selected{% endif %}>Name (Z-A)</option>
                        <option value="-rating" {% if sort_by == '-rating' %}selected{% endif %}>Rating (High to Low)</option>
                        <option value="rating" {% if sort_by == 'rating' %}selected{% endif %}>Rating (Low to High)</option>
                        <option value="-created_at" {% if sort_by == '-created_at' %}selected{% endif %}>Newest First</option>
                    </select>

                    <div class="view-toggle">
                        <button type="button" class="view-toggle-button active" data-view="grid">
                            <i class="fas fa-th"></i>
                        </button>
                        <button type="button" class="view-toggle-button" data-view="list">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="provider-grid" id="provider-container">
                {% for provider in providers %}
                    <div class="provider-card">
                        <div class="provider-card-image">
                            {% if provider.profile_picture %}
                                <img src="{{ provider.profile_picture.url }}" alt="{{ provider.user.get_full_name|default:provider.user.username }}">
                            {% else %}
                                <div class="provider-placeholder">
                                    <i class="fas fa-user"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="provider-card-content">
                            <h3 class="provider-card-title">{{ provider.user.get_full_name|default:provider.user.username }}</h3>
                            <div class="provider-card-category">
                                {% for category in provider.categories.all|slice:":2" %}
                                    {{ category.name }}{% if not forloop.last %}, {% endif %}
                                {% endfor %}
                                {% if provider.categories.count > 2 %}
                                    +{{ provider.categories.count|add:"-2" }} more
                                {% endif %}
                            </div>

                            <div class="provider-card-rating">
                                <div class="provider-card-rating-stars">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= provider.rating %}
                                            <i class="fas fa-star"></i>
                                        {% elif forloop.counter <= provider.rating|add:0.5 %}
                                            <i class="fas fa-star-half-alt"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <div class="provider-card-rating-count">({{ provider.reviews_count }})</div>
                            </div>

                            <p class="provider-card-description">{{ provider.bio|truncatechars:100 }}</p>

                            <div class="provider-card-services">
                                {% for service in provider.services.all|slice:":3" %}
                                    <span class="provider-card-service">{{ service.name }}</span>
                                {% endfor %}
                                {% if provider.services.count > 3 %}
                                    <span class="provider-card-service">+{{ provider.services.count|add:"-3" }} more</span>
                                {% endif %}
                            </div>

                            <a href="{% url 'provider-detail' pk=provider.pk %}" class="btn btn-primary">View Details</a>
                        </div>
                    </div>
                {% empty %}
                    <div class="empty-state">
                        <p>No service providers found matching your criteria.</p>
                        <a href="{% url 'provider-list' %}" class="btn btn-primary">Clear Filters</a>
                    </div>
                {% endfor %}
            </div>

            {% if is_paginated %}
                <div class="pagination-container">
                    <ul class="pagination">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="page-link">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="page-link">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-angle-double-left"></i></span>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-angle-left"></i></span>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="page-link">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="page-link">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="page-link">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-angle-right"></i></span>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-angle-double-right"></i></span>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Sort providers
        const sortSelect = document.getElementById('sort-select');

        if (sortSelect) {
            sortSelect.addEventListener('change', function() {
                const currentUrl = new URL(window.location.href);
                currentUrl.searchParams.set('sort_by', this.value);
                window.location.href = currentUrl.toString();
            });
        }

        // Toggle view (grid/list)
        const viewButtons = document.querySelectorAll('.view-toggle-button');
        const providerContainer = document.getElementById('provider-container');

        if (viewButtons.length && providerContainer) {
            viewButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    viewButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to clicked button
                    this.classList.add('active');

                    // Change view
                    const view = this.getAttribute('data-view');

                    if (view === 'grid') {
                        providerContainer.className = 'provider-grid';
                    } else if (view === 'list') {
                        providerContainer.className = 'provider-list';

                        // Convert grid cards to list items
                        const providerCards = document.querySelectorAll('.provider-card');

                        providerCards.forEach(card => {
                            card.className = 'provider-list-item';

                            const image = card.querySelector('.provider-card-image');
                            const content = card.querySelector('.provider-card-content');

                            if (image) {
                                image.className = 'provider-list-image';
                            }

                            if (content) {
                                content.className = 'provider-list-content';

                                // Update title, category, rating, and services classes
                                const title = content.querySelector('.provider-card-title');
                                const category = content.querySelector('.provider-card-category');
                                const rating = content.querySelector('.provider-card-rating');
                                const description = content.querySelector('.provider-card-description');
                                const services = content.querySelector('.provider-card-services');

                                if (title) {
                                    title.className = 'provider-list-title';
                                }

                                if (category) {
                                    category.className = 'provider-list-category';
                                }

                                if (rating) {
                                    rating.className = 'provider-list-rating';
                                }

                                if (description) {
                                    description.className = 'provider-list-description';
                                }

                                if (services) {
                                    services.className = 'provider-list-services';
                                }
                            }
                        });
                    }
                });
            });
        }

        // Auto-submit rating filter
        const ratingInputs = document.querySelectorAll('.rating-option input');

        if (ratingInputs.length) {
            ratingInputs.forEach(input => {
                input.addEventListener('change', function() {
                    this.closest('form').submit();
                });
            });
        }
    });
</script>
{% endblock %}
